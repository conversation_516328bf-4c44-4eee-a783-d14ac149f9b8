// All Table Attributes
[
  {
    "table_name": "match_histories",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_histories",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "current_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "match_duration_seconds",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_histories",
    "column_name": "player_ids",
    "data_type": "jsonb",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_histories",
    "column_name": "ended_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "match_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_history_players",
    "column_name": "score",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "final_rank",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "elimination_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_history_players",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "match_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "player_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "score",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "lives",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "is_spectator",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "has_answered",
    "data_type": "boolean",
    "is_nullable": "YES"
  },
  {
    "table_name": "match_players",
    "column_name": "lastAnswer",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "longest_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "match_players",
    "column_name": "current_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "matches",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "matches",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_round",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_state",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "current_word_id",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "waiting_time",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "start_time",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "available_word_ids",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "matches",
    "column_name": "is_locked",
    "data_type": "boolean",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "players",
    "column_name": "avatar_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "stats",
    "data_type": "jsonb",
    "is_nullable": "YES"
  },
  {
    "table_name": "players",
    "column_name": "display_name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "user_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "average_response_time",
    "data_type": "numeric",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "highest_round_reached",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "correct_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "total_answers",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "weekly_scores",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "overall_scores",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "matches_played",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "table_name": "user_stats",
    "column_name": "longest_streak",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "words",
    "column_name": "id",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "table_name": "words",
    "column_name": "word",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "table_name": "words",
    "column_name": "difficulty",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "table_name": "words",
    "column_name": "audio_clip_url",
    "data_type": "text",
    "is_nullable": "YES"
  }
]


// All Table Relationships (Foreign Keys)
[
  {
    "source_table": "match_history_players",
    "source_column": "match_id",
    "target_table": "match_histories",
    "target_column": "id"
  },
  {
    "source_table": "match_history_players",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "match_players",
    "source_column": "match_id",
    "target_table": "matches",
    "target_column": "id"
  },
  {
    "source_table": "match_players",
    "source_column": "player_id",
    "target_table": "players",
    "target_column": "id"
  },
  {
    "source_table": "user_stats",
    "source_column": "user_id",
    "target_table": "players",
    "target_column": "id"
  }
]